<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>Experience - <PERSON></title>
</head>
<body>
    <header class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo" aria-label="<PERSON> V Tan - Home">
                <span class="logo-text">@leugimnat</span>
            </a>
            
            <nav class="nav-menu" role="navigation" aria-label="Main navigation">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fa-solid fa-house"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="education.html" class="nav-link">
                            <i class="fa-solid fa-graduation-cap"></i>
                            <span>Education</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="experience.html" class="nav-link active" aria-current="page">
                            <i class="fa-solid fa-briefcase"></i>
                            <span>Experience</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="portfolio.html" class="nav-link">
                            <i class="fa-solid fa-folder-open"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="index.html#contact" class="nav-link">
                            <i class="fa-solid fa-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <button type="button" class="mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>

    <main class="experience-page">
        <section class="experience-hero">
            <div class="hero-content">
                <h1>Professional <span>Experience</span></h1>
                <p class="hero-subtitle">Building expertise through hands-on projects, internships, and continuous learning</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">4+</span>
                        <span class="stat-label">Years Learning</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">10+</span>
                        <span class="stat-label">Projects Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5+</span>
                        <span class="stat-label">Technologies Mastered</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="work-experience">
            <div class="container">
                <h2 class="section-title">Work <span>Experience</span></h2>
                
                <div class="experience-timeline">
                    <div class="experience-item">
                        <div class="experience-marker">
                            <i class="fa-solid fa-laptop-code"></i>
                        </div>
                        <div class="experience-content">
                            <div class="experience-header">
                                <div class="job-info">
                                    <h3>Full-Stack Developer Intern</h3>
                                    <h4 class="company">Tech Solutions Inc.</h4>
                                    <p class="location">Cagayan de Oro City, Philippines</p>
                                </div>
                                <span class="experience-date">Jun 2024 - Aug 2024</span>
                            </div>
                            <div class="experience-details">
                                <p class="job-description">
                                    Developed and maintained web applications using the MERN stack. Collaborated with senior developers 
                                    to implement new features and optimize existing code for better performance.
                                </p>
                                <h5>Key Responsibilities:</h5>
                                <ul class="responsibility-list">
                                    <li>Built responsive web interfaces using React.js and modern CSS</li>
                                    <li>Developed RESTful APIs using Node.js and Express.js</li>
                                    <li>Implemented database schemas and queries using MongoDB</li>
                                    <li>Participated in code reviews and agile development processes</li>
                                    <li>Collaborated with UI/UX designers to implement pixel-perfect designs</li>
                                </ul>
                                <div class="tech-stack-used">
                                    <h5>Technologies Used:</h5>
                                    <div class="tech-tags">
                                        <span class="tech-tag">React</span>
                                        <span class="tech-tag">Node.js</span>
                                        <span class="tech-tag">MongoDB</span>
                                        <span class="tech-tag">Express.js</span>
                                        <span class="tech-tag">Git</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="experience-marker">
                            <i class="fa-solid fa-graduation-cap"></i>
                        </div>
                        <div class="experience-content">
                            <div class="experience-header">
                                <div class="job-info">
                                    <h3>IT Support Assistant</h3>
                                    <h4 class="company">Xavier University - IT Department</h4>
                                    <p class="location">Cagayan de Oro City, Philippines</p>
                                </div>
                                <span class="experience-date">Jan 2023 - May 2024</span>
                            </div>
                            <div class="experience-details">
                                <p class="job-description">
                                    Provided technical support to faculty and students, maintained computer labs, 
                                    and assisted in network administration tasks.
                                </p>
                                <h5>Key Responsibilities:</h5>
                                <ul class="responsibility-list">
                                    <li>Troubleshot hardware and software issues for 200+ users</li>
                                    <li>Maintained and updated computer lab systems</li>
                                    <li>Assisted in network configuration and maintenance</li>
                                    <li>Created technical documentation and user guides</li>
                                    <li>Provided training sessions for new software implementations</li>
                                </ul>
                                <div class="tech-stack-used">
                                    <h5>Technologies Used:</h5>
                                    <div class="tech-tags">
                                        <span class="tech-tag">Windows Server</span>
                                        <span class="tech-tag">Active Directory</span>
                                        <span class="tech-tag">Network Administration</span>
                                        <span class="tech-tag">Hardware Troubleshooting</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="experience-item">
                        <div class="experience-marker">
                            <i class="fa-solid fa-code"></i>
                        </div>
                        <div class="experience-content">
                            <div class="experience-header">
                                <div class="job-info">
                                    <h3>Freelance Web Developer</h3>
                                    <h4 class="company">Self-Employed</h4>
                                    <p class="location">Remote</p>
                                </div>
                                <span class="experience-date">Mar 2022 - Present</span>
                            </div>
                            <div class="experience-details">
                                <p class="job-description">
                                    Developed custom websites and web applications for small businesses and individuals. 
                                    Managed projects from conception to deployment.
                                </p>
                                <h5>Key Responsibilities:</h5>
                                <ul class="responsibility-list">
                                    <li>Designed and developed responsive websites using HTML, CSS, and JavaScript</li>
                                    <li>Created dynamic web applications with modern frameworks</li>
                                    <li>Implemented content management systems for client needs</li>
                                    <li>Provided ongoing maintenance and support services</li>
                                    <li>Managed client relationships and project timelines</li>
                                </ul>
                                <div class="tech-stack-used">
                                    <h5>Technologies Used:</h5>
                                    <div class="tech-tags">
                                        <span class="tech-tag">HTML5</span>
                                        <span class="tech-tag">CSS3</span>
                                        <span class="tech-tag">JavaScript</span>
                                        <span class="tech-tag">React</span>
                                        <span class="tech-tag">WordPress</span>
                                        <span class="tech-tag">PHP</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="project-experience">
            <div class="container">
                <h2 class="section-title">Notable <span>Projects</span></h2>
                <div class="projects-grid">
                    <div class="project-card">
                        <div class="project-icon">
                            <i class="fa-solid fa-tools"></i>
                        </div>
                        <h3>Work Order Management System</h3>
                        <p class="project-description">
                            A comprehensive web-based system for managing work orders, featuring user authentication, 
                            real-time notifications, and administrative analytics.
                        </p>
                        <div class="project-features">
                            <h5>Key Features:</h5>
                            <ul>
                                <li>Google OAuth integration</li>
                                <li>Real-time email notifications</li>
                                <li>Administrative dashboard</li>
                                <li>Mobile-responsive design</li>
                            </ul>
                        </div>
                        <div class="project-tech">
                            <span class="tech-tag">MERN Stack</span>
                            <span class="tech-tag">Google Auth</span>
                            <span class="tech-tag">Email API</span>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-icon">
                            <i class="fa-solid fa-globe"></i>
                        </div>
                        <h3>Portfolio Website</h3>
                        <p class="project-description">
                            A modern, responsive portfolio website showcasing skills, projects, and professional experience 
                            with clean design and smooth animations.
                        </p>
                        <div class="project-features">
                            <h5>Key Features:</h5>
                            <ul>
                                <li>Responsive design</li>
                                <li>Smooth animations</li>
                                <li>SEO optimized</li>
                                <li>Fast loading times</li>
                            </ul>
                        </div>
                        <div class="project-tech">
                            <span class="tech-tag">HTML5</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">JavaScript</span>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-icon">
                            <i class="fa-solid fa-database"></i>
                        </div>
                        <h3>Database Management System</h3>
                        <p class="project-description">
                            A robust database management system for academic records with advanced search capabilities 
                            and data visualization features.
                        </p>
                        <div class="project-features">
                            <h5>Key Features:</h5>
                            <ul>
                                <li>Advanced search filters</li>
                                <li>Data visualization</li>
                                <li>Export functionality</li>
                                <li>User role management</li>
                            </ul>
                        </div>
                        <div class="project-tech">
                            <span class="tech-tag">MySQL</span>
                            <span class="tech-tag">PHP</span>
                            <span class="tech-tag">Chart.js</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="achievements">
            <div class="container">
                <h2 class="section-title">Achievements & <span>Recognition</span></h2>
                <div class="achievements-grid">
                    <div class="achievement-card">
                        <div class="achievement-icon">
                            <i class="fa-solid fa-trophy"></i>
                        </div>
                        <h3>Dean's List</h3>
                        <p>Recognized for academic excellence with consistent high performance throughout university studies.</p>
                        <span class="achievement-year">2022-2024</span>
                    </div>

                    <div class="achievement-card">
                        <div class="achievement-icon">
                            <i class="fa-solid fa-award"></i>
                        </div>
                        <h3>Best Capstone Project</h3>
                        <p>Awarded for outstanding capstone project demonstrating innovation and technical excellence.</p>
                        <span class="achievement-year">2024</span>
                    </div>

                    <div class="achievement-card">
                        <div class="achievement-icon">
                            <i class="fa-solid fa-users"></i>
                        </div>
                        <h3>Team Leadership</h3>
                        <p>Successfully led multiple group projects, demonstrating strong leadership and collaboration skills.</p>
                        <span class="achievement-year">2023-2024</span>
                    </div>

                    <div class="achievement-card">
                        <div class="achievement-icon">
                            <i class="fa-solid fa-lightbulb"></i>
                        </div>
                        <h3>Innovation Award</h3>
                        <p>Recognized for innovative solutions in software development and creative problem-solving approaches.</p>
                        <span class="achievement-year">2023</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="footer-inner">
            <div class="footer-main">
                <div class="footer-brand">
                    <h3 class="footer-logo">@leugimnat</h3>
                    <p class="footer-name">&copy; <span id="year"></span> Miguel Antonio V Tan</p>
                    <p class="footer-status">
                        <span class="status-indicator"></span>
                        Open to Front-End / Full-Stack Roles
                    </p>
                </div>

                <div class="footer-links">
                    <div class="footer-contact">
                        <h4>Get In Touch</h4>
                        <div class="contact-info">
                            <a href="mailto:<EMAIL>" class="contact-email">
                                <i class="fa-solid fa-envelope"></i>
                                <EMAIL>
                            </a>
                            <div class="contact-location">
                                <i class="fa-solid fa-location-dot"></i>
                                <span>Cagayan de Oro City, Philippines</span>
                            </div>
                        </div>
                    </div>

                    <div class="footer-social">
                        <h4>Connect</h4>
                        <div class="social-links">
                            <a href="https://github.com/leugimnat" aria-label="GitHub Profile" title="GitHub">
                                <i class="fa-brands fa-github"></i>
                                <span>GitHub</span>
                            </a>
                            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile" title="LinkedIn">
                                <i class="fa-brands fa-linkedin"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile" title="Facebook">
                                <i class="fa-brands fa-facebook"></i>
                                <span>Facebook</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-tech">
                    <span class="tech-label">Tech Stack:</span>
                    <div class="tech-stack">
                        <span class="tech-item">HTML</span>
                        <span class="tech-item">CSS</span>
                        <span class="tech-item">JavaScript</span>
                    </div>
                </div>
                <p class="footer-note">
                    <i class="fa-solid fa-heart"></i>
                    Crafted with care • Accessible & responsive • Last updated Aug 2025
                </p>
            </div>
        </div>
    </footer>

    <!-- Gallery Modal -->
    <div class="gallery-modal" id="galleryModal">
        <div class="modal-content">
            <img class="modal-image" id="modalImage" src="" alt="">
            <button type="button" class="modal-close" id="modalClose" aria-label="Close modal">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
    </div>

    <script>
        // Update year
        document.getElementById('year').textContent = new Date().getFullYear();
        
        // Mobile menu functionality
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const navLinks = document.querySelectorAll('.nav-link');
        const navbar = document.querySelector('.navbar');
        
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            
            // Update aria-expanded attribute
            const isExpanded = navMenu.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
        });
        
        // Close mobile menu when clicking on nav links
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>
